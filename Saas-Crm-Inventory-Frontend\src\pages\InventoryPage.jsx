import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
    Table,
    TableHeader,
    TableBody,
    TableRow,
    TableHead,
    TableCell
} from '@/components/ui/table';
import {
    Dialog,
    DialogTrigger,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';
import {
    AlertDialog,
    AlertDialogTrigger,
    AlertDialogContent,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogFooter,
    AlertDialogCancel,
    AlertDialogAction
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Pencil, Trash2, PlusCircle, MessageSquare, History, Package, Minus, Plus } from 'lucide-react';

function InventoryPage() {
    const [products, setProducts] = useState([
        {
            id: 'prod1',
            name: 'Laptop Pro X',
            sku: 'LPX-001',
            quantity: 50,
            price: 1200.00,
            category: 'Electronics',
            supplier: 'TechCorp',
            description: 'High-performance laptop for professionals.',
            lastUpdated: new Date(2023, 10, 1, 10, 0),
            notes: [{ id: 'n1', text: 'New model, high demand.', date: new Date(2023, 10, 1, 10, 5) }],
            stockHistory: [
                { id: 'sh1', type: 'received', quantity: 100, date: new Date(2023, 9, 25), notes: 'Initial stock' },
                { id: 'sh2', type: 'sold', quantity: 50, date: new Date(2023, 10, 1), notes: 'Black Friday sales' },
            ],
        },
        {
            id: 'prod2',
            name: 'Ergonomic Keyboard',
            sku: 'EK-005',
            quantity: 15, // Low stock example
            price: 75.50,
            category: 'Peripherals',
            supplier: 'ErgoGear',
            description: 'Comfortable keyboard for extended use.',
            lastUpdated: new Date(2023, 10, 15, 14, 30),
            notes: [],
            stockHistory: [
                { id: 'sh3', type: 'received', quantity: 30, date: new Date(2023, 10, 10), notes: 'Monthly restock' },
                { id: 'sh4', type: 'sold', quantity: 15, date: new Date(2023, 10, 15), notes: 'Online order' },
            ],
        },
        {
            id: 'prod3',
            name: 'Wireless Mouse',
            sku: 'WM-010',
            quantity: 120,
            price: 25.00,
            category: 'Peripherals',
            supplier: 'TechCorp',
            description: 'Compact and precise wireless mouse.',
            lastUpdated: new Date(2023, 11, 1, 9, 0),
            notes: [{ id: 'n4', text: 'Bundled with Laptop Pro X.', date: new Date(2023, 11, 1, 9, 10) }],
            stockHistory: [
                { id: 'sh5', type: 'received', quantity: 200, date: new Date(2023, 10, 28), notes: 'Bulk purchase' },
                { id: 'sh6', type: 'sold', quantity: 80, date: new Date(2023, 11, 1), notes: 'Holiday sales' },
            ],
        },
    ]);

    const availableCategories = ['Electronics', 'Peripherals', 'Accessories', 'Software', 'Components'];
    const [availableSuppliers, setAvailableSuppliers] = useState(['TechCorp', 'ErgoGear', 'GlobalSupply', 'InnovateParts']);
    const LOW_STOCK_THRESHOLD = 20;

    const [showAdd, setShowAdd] = useState(false);
    const [showEdit, setShowEdit] = useState(false);
    const [currentProduct, setCurrentProduct] = useState(null);
    const [form, setForm] = useState({
        name: '', sku: '', quantity: 0, price: 0.00, category: '', supplier: '', description: ''
    });
    const [newNoteText, setNewNoteText] = useState('');
    const [stockAdjustmentQuantity, setStockAdjustmentQuantity] = useState(0);
    const [stockAdjustmentNotes, setStockAdjustmentNotes] = useState('');
    const [newSupplierName, setNewSupplierName] = useState('');

    const [productToDeleteId, setProductToDeleteId] = useState(null);
    const [validationError, setValidationError] = useState('');
    const [activeTab, setActiveTab] = useState('general');

    const handleInputChange = (e) => {
        const { id, value } = e.target;
        setForm(prev => ({ ...prev, [id]: value }));
        setValidationError('');
    };

    const handleStockAdjustmentQuantityChange = (e) => {
        setStockAdjustmentQuantity(Number(e.target.value));
    };

    const handleStockAdjustmentNotesChange = (e) => {
        setStockAdjustmentNotes(e.target.value);
    };

    const handleNewSupplierNameChange = (e) => {
        setNewSupplierName(e.target.value);
    };

    const handleAddProduct = () => {
        if (!form.name || !form.sku || form.quantity < 0 || form.price < 0) {
            setValidationError("Item Name, SKU, Quantity (>=0), and Price (>=0) are required.");
            return;
        }
        const newProduct = {
            id: String(Date.now()),
            ...form,
            quantity: Number(form.quantity),
            price: Number(form.price),
            lastUpdated: new Date(),
            notes: [],
            stockHistory: [],
        };
        setProducts([newProduct, ...products]);
        setForm({ name: '', sku: '', quantity: 0, price: 0.00, category: '', supplier: '', description: '' });
        setValidationError('');
        setShowAdd(false);
    };

    const handleUpdateProductGeneralInfo = () => {
        if (!form.name || !form.sku || form.quantity < 0 || form.price < 0) {
            setValidationError("Item Name, SKU, Quantity (>=0), and Price (>=0) are required.");
            return;
        }
        setProducts(
            products.map(product =>
                product.id === currentProduct.id ? { ...product, ...form, quantity: Number(form.quantity), price: Number(form.price), lastUpdated: new Date() } : product
            )
        );
        setValidationError('');
    };

    const handleAddNewNote = () => {
        if (newNoteText.trim() === '') {
            return;
        }
        if (currentProduct) {
            const updatedNotes = [
                ...(currentProduct.notes || []),
                {
                    id: String(Date.now()),
                    text: newNoteText,
                    date: new Date(),
                },
            ];
            setCurrentProduct(prev => ({ ...prev, notes: updatedNotes }));
            setProducts(prevProducts =>
                prevProducts.map(product =>
                    product.id === currentProduct.id ? { ...product, notes: updatedNotes } : product
                )
            );
            setNewNoteText('');
        }
    };

    const handleStockAdjustment = (type) => {
        if (stockAdjustmentQuantity <= 0) {
            setValidationError("Quantity for adjustment must be greater than 0.");
            return;
        }
        if (currentProduct) {
            let newQuantity = currentProduct.quantity;
            if (type === 'increase') {
                newQuantity += stockAdjustmentQuantity;
            } else {
                if (newQuantity - stockAdjustmentQuantity < 0) {
                    setValidationError("Cannot decrease stock below zero.");
                    return;
                }
                newQuantity -= stockAdjustmentQuantity;
            }

            const newStockHistoryEntry = {
                id: String(Date.now()),
                type: type,
                quantity: stockAdjustmentQuantity,
                date: new Date(),
                notes: stockAdjustmentNotes.trim() || (type === 'increase' ? 'Stock received' : 'Stock sold/removed'),
            };

            const updatedStockHistory = [
                ...(currentProduct.stockHistory || []),
                newStockHistoryEntry,
            ].sort((a, b) => b.date - a.date);

            setCurrentProduct(prev => ({
                ...prev,
                quantity: newQuantity,
                stockHistory: updatedStockHistory,
                lastUpdated: new Date(),
            }));
            setProducts(prevProducts =>
                prevProducts.map(product =>
                    product.id === currentProduct.id ? { ...product, quantity: newQuantity, stockHistory: updatedStockHistory, lastUpdated: new Date() } : product
                )
            );
            setStockAdjustmentQuantity(0);
            setStockAdjustmentNotes('');
            setValidationError('');
        }
    };

    const handleAddSupplier = () => {
        if (newSupplierName.trim() === '') {
            setValidationError("Supplier name cannot be empty.");
            return;
        }
        if (!availableSuppliers.includes(newSupplierName.trim())) {
            setAvailableSuppliers(prev => [...prev, newSupplierName.trim()]);
            setNewSupplierName('');
            setValidationError('');
        } else {
            setValidationError("Supplier already exists.");
        }
    };

    const handleDeleteProductConfirmed = () => {
        setProducts(products.filter(product => product.id !== productToDeleteId));
        setProductToDeleteId(null);
    };

    const openAddModal = () => {
        setForm({ name: '', sku: '', quantity: 0, price: 0.00, category: availableCategories[0] || '', supplier: availableSuppliers[0] || '', description: '' });
        setValidationError('');
        setShowAdd(true);
    };

    const openEditModal = (product) => {
        setCurrentProduct(product);
        setForm({
            name: product.name,
            sku: product.sku,
            quantity: product.quantity,
            price: product.price,
            category: product.category,
            supplier: product.supplier,
            description: product.description,
        });
        setNewNoteText('');
        setStockAdjustmentQuantity(0);
        setStockAdjustmentNotes('');
        setNewSupplierName(''); // Clear new supplier input
        setValidationError('');
        setActiveTab('general');
        setShowEdit(true);
    };

    const formatDate = (date) => {
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
        }).format(date);
    };

    return (
        <Card className="p-4 rounded-none">
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Inventory Management</CardTitle>
                <div className="flex items-center space-x-2">
                    <Dialog open={showAdd} onOpenChange={setShowAdd}>
                        <DialogTrigger asChild>
                            <Button onClick={openAddModal}>
                                <PlusCircle className="mr-2 h-4 w-4" /> Add Product
                            </Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-[800px] h-[90vh] flex flex-col">
                            <DialogHeader>
                                <DialogTitle>Add New Product</DialogTitle>
                            </DialogHeader>
                            <div className="flex-grow overflow-y-auto pr-2">
                                <div className="space-y-4 py-4">
                                    <div>
                                        <Label htmlFor="name" className="pb-3">Item Name</Label>
                                        <Input id="name" value={form.name} onChange={handleInputChange} />
                                    </div>
                                    <div>
                                        <Label htmlFor="sku" className="pb-3">SKU</Label>
                                        <Input id="sku" value={form.sku} onChange={handleInputChange} />
                                    </div>
                                    <div>
                                        <Label htmlFor="quantity" className="pb-3">Quantity</Label>
                                        <Input id="quantity" type="number" value={form.quantity} onChange={handleInputChange} />
                                    </div>
                                    <div>
                                        <Label htmlFor="price" className="pb-3">Price</Label>
                                        <Input id="price" type="number" step="0.01" value={form.price} onChange={handleInputChange} />
                                    </div>
                                    <div>
                                        <Label htmlFor="category" className="pb-3">Category</Label>
                                        <select
                                            id="category"
                                            value={form.category}
                                            onChange={handleInputChange}
                                            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                        >
                                            {availableCategories.map(cat => (
                                                <option key={cat} value={cat}>{cat}</option>
                                            ))}
                                        </select>
                                    </div>
                                    <div>
                                        <Label htmlFor="supplier" className="pb-3">Supplier</Label>
                                        <select
                                            id="supplier"
                                            value={form.supplier}
                                            onChange={handleInputChange}
                                            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                        >
                                            {availableSuppliers.map(supp => (
                                                <option key={supp} value={supp}>{supp}</option>
                                            ))}
                                        </select>
                                    </div>
                                    <div>
                                        <Label htmlFor="description" className="pb-3">Description</Label>
                                        <Textarea id="description" value={form.description} onChange={handleInputChange} />
                                    </div>
                                    {validationError && <p className="text-red-500 text-sm">{validationError}</p>}
                                </div>
                            </div>
                            <DialogFooter className="mt-4 flex justify-end space-x-2">
                                <Button onClick={handleAddProduct}>Add Product</Button>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>
                </div>
            </CardHeader>

            <CardContent>
                {products.length === 0 ? (
                    <div className="text-center text-gray-500 py-10">No products found. Add a new one to get started!</div>
                ) : (
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead className="w-[50px]">#</TableHead>
                                <TableHead>Item Name</TableHead>
                                <TableHead>SKU</TableHead>
                                <TableHead>Quantity</TableHead>
                                <TableHead>Price</TableHead>
                                <TableHead>Category</TableHead>
                                <TableHead>Last Updated</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {products.map((product, index) => (
                                <TableRow key={product.id} className={product.quantity <= LOW_STOCK_THRESHOLD ? 'bg-yellow-50' : ''}>
                                    <TableCell>{index + 1}</TableCell>
                                    <TableCell>{product.name}</TableCell>
                                    <TableCell>{product.sku}</TableCell>
                                    <TableCell className={product.quantity <= LOW_STOCK_THRESHOLD ? 'font-bold text-red-600' : ''}>
                                        {product.quantity} {product.quantity <= LOW_STOCK_THRESHOLD && <span className="text-xs text-red-500">(Low Stock!)</span>}
                                    </TableCell>
                                    <TableCell>${product.price.toFixed(2)}</TableCell>
                                    <TableCell>{product.category}</TableCell>
                                    <TableCell>{formatDate(product.lastUpdated)}</TableCell>
                                    <TableCell className="text-right space-x-2">
                                        {/* Edit Dialog */}
                                        <Dialog open={showEdit && currentProduct?.id === product.id} onOpenChange={setShowEdit}>
                                            <DialogTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => openEditModal(product)}
                                                >
                                                    <Pencil className="h-4 w-4" />
                                                </Button>
                                            </DialogTrigger>
                                            {currentProduct && (
                                                <DialogContent className="sm:max-w-[800px] h-[90vh] flex flex-col">
                                                    <DialogHeader>
                                                        <DialogTitle className="text-2xl font-bold">{currentProduct.name || 'Edit Product'}</DialogTitle>
                                                    </DialogHeader>
                                                    <div className="flex-grow overflow-hidden flex flex-col">
                                                        {/* Tabs Navigation */}
                                                        <div className="border-b mb-4">
                                                            <nav className="-mb-px flex space-x-8" aria-label="Tabs">
                                                                <button
                                                                    onClick={() => setActiveTab('general')}
                                                                    className={`${activeTab === 'general' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                                                                >
                                                                    General
                                                                </button>
                                                                <button
                                                                    onClick={() => setActiveTab('stockHistory')}
                                                                    className={`${activeTab === 'stockHistory' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                                                                >
                                                                    Stock History
                                                                </button>
                                                                <button
                                                                    onClick={() => setActiveTab('notes')}
                                                                    className={`${activeTab === 'notes' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                                                                >
                                                                    Notes
                                                                </button>
                                                                <button
                                                                    onClick={() => setActiveTab('suppliers')}
                                                                    className={`${activeTab === 'suppliers' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                                                                >
                                                                    Suppliers
                                                                </button>
                                                            </nav>
                                                        </div>

                                                        {/* Tab Content */}
                                                        <div className="flex-grow overflow-y-auto pr-2">
                                                            {activeTab === 'general' && (
                                                                <div className="space-y-6 py-4">
                                                                    {/* Basic Information Block */}
                                                                    <div className="border p-4 rounded-md bg-gray-50 space-y-4">
                                                                        <h4 className="text-lg font-semibold mb-2">Basic Information</h4>
                                                                        <div className="grid grid-cols-4 items-center gap-4">
                                                                            <Label htmlFor="name" className="text-right">Item Name</Label>
                                                                            <Input id="name" value={form.name} onChange={handleInputChange} className="col-span-3" />
                                                                        </div>
                                                                        <div className="grid grid-cols-4 items-center gap-4">
                                                                            <Label htmlFor="sku" className="text-right">SKU</Label>
                                                                            <Input id="sku" value={form.sku} onChange={handleInputChange} className="col-span-3" />
                                                                        </div>
                                                                        <div className="grid grid-cols-4 items-center gap-4">
                                                                            <Label htmlFor="quantity" className="text-right">Quantity</Label>
                                                                            <Input id="quantity" type="number" value={form.quantity} onChange={handleInputChange} className="col-span-3" />
                                                                        </div>
                                                                        <div className="grid grid-cols-4 items-center gap-4">
                                                                            <Label htmlFor="price" className="text-right">Price</Label>
                                                                            <Input id="price" type="number" step="0.01" value={form.price} onChange={handleInputChange} className="col-span-3" />
                                                                        </div>
                                                                        <div className="grid grid-cols-4 items-center gap-4">
                                                                            <Label htmlFor="category" className="text-right">Category</Label>
                                                                            <select
                                                                                id="category"
                                                                                value={form.category}
                                                                                onChange={handleInputChange}
                                                                                className="col-span-3 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                                                            >
                                                                                {availableCategories.map(cat => (
                                                                                    <option key={cat} value={cat}>{cat}</option>
                                                                                ))}
                                                                            </select>
                                                                        </div>
                                                                        <div className="grid grid-cols-4 items-center gap-4">
                                                                            <Label htmlFor="supplier" className="text-right">Supplier</Label>
                                                                            <select
                                                                                id="supplier"
                                                                                value={form.supplier}
                                                                                onChange={handleInputChange}
                                                                                className="col-span-3 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                                                            >
                                                                                {availableSuppliers.map(supp => (
                                                                                    <option key={supp} value={supp}>{supp}</option>
                                                                                ))}
                                                                            </select>
                                                                        </div>
                                                                        <div className="grid grid-cols-4 items-center gap-4">
                                                                            <Label htmlFor="description" className="text-right">Description</Label>
                                                                            <Textarea id="description" value={form.description} onChange={handleInputChange} className="col-span-3 h-24 resize-y" />
                                                                        </div>
                                                                    </div>

                                                                    {validationError && <p className="text-red-500 text-sm">{validationError}</p>}
                                                                </div>
                                                            )}

                                                            {activeTab === 'stockHistory' && (
                                                                <div className="space-y-3 pt-4">
                                                                    <h3 className="text-lg font-semibold flex items-center">
                                                                        <History className="mr-2 h-5 w-5 text-gray-600" /> Stock Adjustments
                                                                    </h3>
                                                                    <div className="border p-4 rounded-md bg-gray-50 space-y-4">
                                                                        <h4 className="text-md font-semibold mb-2">Adjust Stock</h4>
                                                                        <div className="flex items-end space-x-2">
                                                                            <div className="flex-grow">
                                                                                <Label htmlFor="stockQuantity" className="pb-1">Quantity</Label>
                                                                                <Input
                                                                                    id="stockQuantity"
                                                                                    type="number"
                                                                                    value={stockAdjustmentQuantity}
                                                                                    onChange={handleStockAdjustmentQuantityChange}
                                                                                    className="w-full"
                                                                                />
                                                                            </div>
                                                                            <div className="flex-grow">
                                                                                <Label htmlFor="stockNotes" className="pb-1">Notes (Optional)</Label>
                                                                                <Input
                                                                                    id="stockNotes"
                                                                                    value={stockAdjustmentNotes}
                                                                                    onChange={handleStockAdjustmentNotesChange}
                                                                                    className="w-full"
                                                                                />
                                                                            </div>
                                                                            <Button onClick={() => handleStockAdjustment('increase')} className="bg-green-500 hover:bg-green-600">
                                                                                <Plus className="h-4 w-4" />
                                                                            </Button>
                                                                            <Button onClick={() => handleStockAdjustment('decrease')} className="bg-red-500 hover:bg-red-600">
                                                                                <Minus className="h-4 w-4" />
                                                                            </Button>
                                                                        </div>
                                                                        {validationError && <p className="text-red-500 text-sm">{validationError}</p>}
                                                                    </div>

                                                                    <div className="max-h-96 overflow-y-auto pr-2 space-y-2 mt-4">
                                                                        {currentProduct?.stockHistory && currentProduct.stockHistory.length > 0 ? (
                                                                            currentProduct.stockHistory.map(entry => (
                                                                                <div key={entry.id} className={`p-2 rounded-md text-sm border ${entry.type === 'received' ? 'bg-blue-50 border-blue-200' : 'bg-red-50 border-red-200'}`}>
                                                                                    <p className="font-medium">
                                                                                        {entry.type === 'received' ? 'Received' : 'Removed'}: {entry.quantity} units
                                                                                    </p>
                                                                                    <p className="text-xs text-gray-700">{entry.notes}</p>
                                                                                    <p className="text-xs text-gray-500 mt-1">{formatDate(entry.date)}</p>
                                                                                </div>
                                                                            ))
                                                                        ) : (
                                                                            <p className="text-sm text-gray-500">No stock history yet.</p>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            )}

                                                            {activeTab === 'notes' && (
                                                                <div className="space-y-3 pt-4">
                                                                    <h3 className="text-lg font-semibold flex items-center">
                                                                        <MessageSquare className="mr-2 h-5 w-5 text-gray-600" /> Notes
                                                                    </h3>
                                                                    <div className="max-h-96 overflow-y-auto pr-2 space-y-2">
                                                                        {currentProduct?.notes && currentProduct.notes.length > 0 ? (
                                                                            currentProduct.notes.map(note => (
                                                                                <div key={note.id} className="p-2 bg-gray-50 rounded-md text-sm border border-gray-200">
                                                                                    <p className="text-gray-800">{note.text}</p>
                                                                                    <p className="text-xs text-gray-500 mt-1">{formatDate(note.date)}</p>
                                                                                </div>
                                                                            ))
                                                                        ) : (
                                                                            <p className="text-sm text-gray-500">No notes yet.</p>
                                                                        )}
                                                                    </div>
                                                                    <div className="flex space-x-2 mt-3">
                                                                        <Textarea
                                                                            placeholder="Add a new note..."
                                                                            value={newNoteText}
                                                                            onChange={(e) => setNewNoteText(e.target.value)}
                                                                            className="flex-grow resize-y"
                                                                            rows={2}
                                                                        />
                                                                        <Button onClick={handleAddNewNote} className="shrink-0 self-end">Add Note</Button>
                                                                    </div>
                                                                </div>
                                                            )}

                                                            {activeTab === 'suppliers' && (
                                                                <div className="space-y-3 pt-4">
                                                                    <h3 className="text-lg font-semibold flex items-center">
                                                                        <Package className="mr-2 h-5 w-5 text-gray-600" /> Manage Suppliers
                                                                    </h3>
                                                                    <div className="border p-4 rounded-md bg-gray-50 space-y-4">
                                                                        <h4 className="text-md font-semibold mb-2">Add New Supplier</h4>
                                                                        <div className="flex items-end space-x-2">
                                                                            <div className="flex-grow">
                                                                                <Label htmlFor="newSupplierName" className="pb-1">Supplier Name</Label>
                                                                                <Input
                                                                                    id="newSupplierName"
                                                                                    value={newSupplierName}
                                                                                    onChange={handleNewSupplierNameChange}
                                                                                    className="w-full"
                                                                                />
                                                                            </div>
                                                                            <Button onClick={handleAddSupplier} className="shrink-0">Add Supplier</Button>
                                                                        </div>
                                                                        {validationError && <p className="text-red-500 text-sm">{validationError}</p>}
                                                                    </div>

                                                                    <div className="max-h-96 overflow-y-auto pr-2 space-y-2 mt-4">
                                                                        <h4 className="text-md font-semibold mb-2">Available Suppliers</h4>
                                                                        {availableSuppliers.length > 0 ? (
                                                                            <ul className="list-disc list-inside space-y-1">
                                                                                {availableSuppliers.map(supplier => (
                                                                                    <li key={supplier} className="p-2 bg-gray-50 rounded-md text-sm border border-gray-200">{supplier}</li>
                                                                                ))}
                                                                            </ul>
                                                                        ) : (
                                                                            <p className="text-sm text-gray-500">No suppliers added yet.</p>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                    <DialogFooter className="mt-4 flex justify-end space-x-2">
                                                        <Button variant="outline" onClick={() => setShowEdit(false)}>Close</Button>
                                                        <Button onClick={handleUpdateProductGeneralInfo}>Save Changes</Button>
                                                    </DialogFooter>
                                                </DialogContent>
                                            )}
                                        </Dialog>

                                        {/* Delete Alert Dialog */}
                                        <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button
                                                    variant="destructive"
                                                    size="sm"
                                                    onClick={() => setProductToDeleteId(product.id)}
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                                </AlertDialogHeader>
                                                <p>This action cannot be undone. This will permanently delete the product.</p>
                                                <AlertDialogFooter>
                                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                    <AlertDialogAction onClick={handleDeleteProductConfirmed}>
                                                        Delete
                                                    </AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                )}
            </CardContent>
        </Card>
    );
}

export default InventoryPage;