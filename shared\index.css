
/* ---------------------------------------------------- */
/* --- Color Variables - <PERSON>y and Serene Palette  --- */
/* ---------------------------------------------------- */

:root {
  --radius: 0.625rem;

  /* General Background & Text Colors */
  --color-background: #FAF3E0; /* Sand */
  --color-foreground: #2B2D42; /* Deep Slate */

  /* Card and Popover Colors */
  --color-card: #FFFFFF;
  --color-card-foreground: #2B2D42;
  --color-popover: #FFFFFF;
  --color-popover-foreground: #2B2D42;

  /* Primary, Accent and Other UI Colors */
  --color-primary: #E67E22; /* Vibrant Orange */
  --color-primary-foreground: #FFFFFF;
  --color-secondary: #3498DB; /* Bright Blue */
  --color-secondary-foreground: #FFFFFF;
  --color-muted: #ECF0F1;
  --color-muted-foreground: #7F8C8D;
  --color-accent: #27AE60; /* Emerald Green for highlight */
  --color-accent-foreground: #FFFFFF;
  --color-destructive: #E74C3C;
  --destructive: #E74C3C;
  /* Borders and Rings */
  --color-border: #BDC3C7;
  --color-input: #BDC3C7;
  --color-ring: #27AE60;

  /* Sidebar and Header Specific Colors */
  --color-sidebar: #2C3E50; /* Dark Blue-Gray */
  --color-sidebar-foreground: #FFFFFF;
  --color-sidebar-accent: var(--color-accent);
  --color-sidebar-accent-foreground: var(--color-accent-foreground);
  --color-sidebar-border: #34495E;
  --color-sidebar-ring: var(--color-ring);

  --color-header: #34495E; /* Dark Blue-Gray for header */
  --color-header-foreground: #FFFFFF;
}

/* Dark theme variables */
[data-theme="dark"] {
  --color-background: #1A202C; /* Dark blue-gray */
  --color-foreground: #F7FAFC; /* Light gray */

  --color-card: #2D3748; /* Dark gray-blue */
  --color-card-foreground: #F7FAFC;
  --color-popover: #2D3748;
  --color-popover-foreground: #F7FAFC;

  --color-primary: #F39C12; /* Bright Orange for dark mode */
  --color-primary-foreground: #FFFFFF;
  --color-secondary: #3498DB; /* Keep blue as secondary */
  --color-secondary-foreground: #FFFFFF;
  --color-muted: #4A5568;
  --color-muted-foreground: #A0AEC0;
  --color-accent: #2ECC71; /* Bright Green for dark mode */
  --color-accent-foreground: #FFFFFF;

  --color-border: #4A5568;
  --color-input: #4A5568;
  --color-ring: #2ECC71;

  --color-sidebar: #1A202C; /* Darker sidebar */
  --color-sidebar-foreground: #F7FAFC;
  --color-sidebar-border: #2D3748;
  --color-header: #2D3748; /* Darker header for dark mode */
  --color-header-foreground: #F7FAFC;
  --color-destructive: #E53E3E;
  --destructive: #E53E3E;
}

/* ---------------------------------------------------- */
/* --- Ant Design & Custom Component Styles       --- */
/* ---------------------------------------------------- */

/* General Layout & Backgrounds */
.ant-layout-sider {
  background: var(--color-sidebar) !important;
  color: var(--color-sidebar-foreground) !important;
}

.ant-layout-header {
  background: var(--color-header) !important;
  color: var(--color-header-foreground) !important;
  display: flex;
  align-items: center;
  padding: 0 16px;
}

.admin-layout-main {
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden; /* Prevent whole page scroll */
  display: flex;
  flex-direction: column;
}

.admin-sider {
  height: calc(100vh - 64px); /* Subtract header height */
  background: var(--color-sidebar);
  box-shadow: 4px 0 32px rgba(44, 62, 80, 0.15);
  transition: all 0.3s ease;
  overflow: hidden; /* Hide overflow on sider itself */
}

.admin-sider-dark {
  box-shadow: 4px 0 32px rgba(0, 0, 0, 0.3);
}

.admin-sider-light {
  box-shadow: 4px 0 32px rgba(44, 62, 80, 0.15);
}

.admin-logo-container {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 24px;
  border-bottom: 2px solid var(--color-sidebar-border);
  background: var(--color-header);
}

.admin-logo-container-collapsed {
  justify-content: center;
  padding: 0;
}

.admin-logo-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: var(--color-accent);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-accent-foreground);
  font-weight: bold;
  font-size: 18px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.admin-text{
  color: var(--color-header-foreground);
  font-weight: 600;
  letter-spacing: 0.5px;
}

.admin-menu-container {
  height: 100%;
  overflow-y: auto; /* Make only menu scrollable */
  overflow-x: hidden;
  padding-right: 8px;
}

/* Custom scrollbar for menu */
.admin-menu-container::-webkit-scrollbar {
  width: 6px;
}

.admin-menu-container::-webkit-scrollbar-track {
  background: transparent;
}

.admin-menu-container::-webkit-scrollbar-thumb {
  background: var(--color-sidebar-accent);
  border-radius: 3px;
}

.admin-menu-container::-webkit-scrollbar-thumb:hover {
  background: var(--color-accent);
}

.admin-layout-content {
  transition: all 0.3s ease;
  flex: 1;
  overflow: hidden;
}

.admin-content-layout {
  height: calc(100vh - 64px); /* Subtract header height */
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.admin-header {
  padding: 0 24px;
  background: var(--color-header);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 10;
  border-bottom: 2px solid var(--color-sidebar-border);
  box-shadow: 0 4px 16px rgba(230, 126, 34, 0.15);
  height: 64px;
  transition: all 0.3s ease;
}

.admin-header-dark {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25);
}

.admin-header-light {
  box-shadow: 0 4px 16px rgba(230, 126, 34, 0.15);
}

.admin-button-header {
  font-size: 16px;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.25);
  color: var(--color-header-foreground);
}

.admin-button-header:hover {
  background: var(--color-accent) !important;
  color: var(--color-accent-foreground) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4) !important;
  border-color: var(--color-accent) !important;
}

.admin-button-user {
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 16px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.25);
  color: var(--color-header-foreground);
}

.admin-button-user:hover {
  background: var(--color-accent) !important;
  color: var(--color-accent-foreground) !important;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(39, 174, 96, 0.4) !important;
  border-color: var(--color-accent) !important;
}

.admin-user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: var(--color-accent);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-accent-foreground);
  font-size: 14px;
  font-weight: bold;
  transition: all 0.3s ease;
}

/* Padded Content Area */
.admin-content-main {
  flex: 1;
  padding: 24px;
  background: var(--color-background);
  overflow-y: auto; /* Make only content scrollable */
  overflow-x: hidden;
}

/* Custom scrollbar for content */
.admin-content-main::-webkit-scrollbar {
  width: 8px;
}

.admin-content-main::-webkit-scrollbar-track {
  background: var(--color-muted);
  border-radius: 4px;
}

.admin-content-main::-webkit-scrollbar-thumb {
  background: var(--color-accent);
  border-radius: 4px;
}

.admin-content-main::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary);
}

.admin-content-card {
  background: var(--color-card);
  border: none !important;
  border-radius: var(--radius);
  min-height: 100%;
}

/* Ant Menu Overrides */
.ant-menu {
  background: var(--color-sidebar) !important;
  color: var(--color-sidebar-foreground) !important;
  border-right: none !important;
}

.ant-menu-item,
.ant-menu-submenu-title {
  border-radius: var(--radius) !important;
  margin: 4px 8px !important;
  color: var(--color-sidebar-foreground) !important;
  transition: all 0.2s ease !important;
}

/* Updated Menu Item Selected State */
.ant-menu-item-selected {
  background: var(--color-sidebar-accent) !important;
  color: var(--color-sidebar-accent-foreground) !important;
  font-weight: 600 !important;
  box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4) !important;
  border-left: none;
  transform: translateX(4px);
}

.ant-menu-item-selected .anticon {
  color: var(--color-sidebar-accent-foreground) !important;
}

/* Submenu parent active state */
.ant-menu-submenu-selected > .ant-menu-submenu-title {
  background: var(--color-sidebar-accent) !important;
  color: var(--color-sidebar-accent-foreground) !important;
  font-weight: 600 !important;
  box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4) !important;
  transform: translateX(4px);
}

.ant-menu-submenu-selected > .ant-menu-submenu-title .anticon {
  color: var(--color-sidebar-accent-foreground) !important;
}

.ant-menu-item:hover,
.ant-menu-submenu-title:hover {
  background: var(--color-sidebar-accent) !important;
  color: var(--color-sidebar-accent-foreground) !important;
  transform: translateX(4px);
  box-shadow: 0 4px 16px rgba(39, 174, 96, 0.3) !important;
}

.ant-menu-item-selected:hover {
  background: var(--color-sidebar-accent) !important;
  color: var(--color-sidebar-accent-foreground) !important;
  transform: translateX(6px);
  box-shadow: 0 8px 24px rgba(39, 174, 96, 0.5) !important;
}

.ant-menu-sub.ant-menu-inline {
  background: transparent !important;
}

.ant-menu-sub .ant-menu-item {
  padding-left: 32px !important;
  margin-left: 8px !important;
  width: calc(100% - 16px) !important;
}

/* These hover effects are now handled in the button classes above */

.ant-layout-content {
  background: var(--color-background);
}

/* Footer styles */
.admin-footer-main {
  padding: 16px 24px;
  background: var(--color-header);
  border-top: 1px solid var(--color-border);
  flex-shrink: 0; /* Prevent footer from shrinking */
}

.admin-footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.admin-footer-text {
  color: var(--color-header-foreground);
  font-size: 14px;
}

.admin-footer-links {
  display: flex;
  gap: 24px;
}

.admin-footer-link {
  color: var(--color-header-foreground);
  font-size: 14px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.admin-footer-link:hover {
  color: var(--color-accent);
}

/* Drawer styles */
.admin-drawer-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.admin-drawer-logo {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: var(--color-sidebar-accent);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-sidebar-accent-foreground);
  font-weight: bold;
  font-size: 18px;
}

/* Theme settings */
.admin-theme-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-radius: var(--radius);
  background: var(--color-card);
  border: 1px solid var(--color-border);
}

.admin-theme-text {
  display: flex;
  align-items: center;
  gap: 12px;
}

.admin-theme-description {
  font-size: 12px;
  color: var(--color-muted-foreground);
}

/* Mobile responsiveness */
@media (max-width: 991px) {
  .admin-layout-content {
    margin-left: 0 !important;
  }

  .admin-header {
    padding: 0 16px;
  }

  .admin-content-main {
    padding: 16px;
  }

  .admin-footer-content {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .admin-footer-links {
    gap: 16px;
  }
}

/* Additional responsive styles */
@media (max-width: 768px) {
  .admin-content-main {
    padding: 12px;
  }

  .admin-header {
    padding: 0 12px;
  }

  .admin-footer-main {
    padding: 12px 16px;
  }

  .admin-footer-links {
    gap: 12px;
  }
}

