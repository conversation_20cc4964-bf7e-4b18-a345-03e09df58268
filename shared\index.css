
/* ---------------------------------------------------- */
/* --- Color Variables - <PERSON>y and Serene Palette  --- */
/* ---------------------------------------------------- */

:root {
  --radius: 0.625rem;

  /* General Background & Text Colors */
  --color-background: #FAF3E0; /* Sand */
  --color-foreground: #2B2D42; /* Deep Slate */

  /* Card and Popover Colors */
  --color-card: #FFFFFF;
  --color-card-foreground: #2B2D42;
  --color-popover: #FFFFFF;
  --color-popover-foreground: #2B2D42;

  /* Primary, Accent and Other UI Colors */
  --color-primary: #D17B49; /* Terracotta */
  --color-primary-foreground: #FFFFFF;
  --color-secondary: #D17B49; /* Consistent with primary */
  --color-secondary-foreground: #FFFFFF;
  --color-muted: #EDE8DF;
  --color-muted-foreground: #8D99AE;
  --color-accent: #A3B18A; /* Sage Green for highlight */
  --color-accent-foreground: #2B2D42;
  --color-destructive: #9B2226;
--destructive: #9B2226;
  /* Borders and Rings */
  --color-border: #E6D9C6;
  --color-input: #E6D9C6;
  --color-ring: #A3B18A;

  /* Sidebar and Header Specific Colors */
  --color-sidebar: var(--color-primary);
  --color-sidebar-foreground: var(--color-primary-foreground);
  --color-sidebar-accent: var(--color-accent);
  --color-sidebar-accent-foreground: var(--color-accent-foreground);
  --color-sidebar-border: #C56A3A;
  --color-sidebar-ring: var(--color-ring);

  --color-header: var(--color-primary);
  --color-header-foreground: var(--color-primary-foreground);
}

/* Dark theme variables */
[data-theme="dark"] {
  --color-background: #1A1F2C; /* Dark blue-gray */
  --color-foreground: #E2E8F0; /* Light gray */
  
  --color-card: #2D3748; /* Dark gray-blue */
  --color-card-foreground: #E2E8F0;
  --color-popover: #2D3748;
  --color-popover-foreground: #E2E8F0;
  
  --color-primary: #D17B49; /* Keep terracotta as primary */
  --color-primary-foreground: #FFFFFF;
  --color-secondary: #A3B18A; /* Use sage as secondary */
  --color-secondary-foreground: #1A1F2C;
  --color-muted: #4A5568;
  --color-muted-foreground: #A0AEC0;
  --color-accent: #D17B49; /* Use terracotta as accent */
  --color-accent-foreground: #FFFFFF;
  
  --color-border: #4A5568;
  --color-input: #4A5568;
  --color-ring: #D17B49;
  
  --color-sidebar-border: #E2A17E;
   --color-destructive: #9B2226;
--destructive: #9B2226;
}

/* ---------------------------------------------------- */
/* --- Ant Design & Custom Component Styles       --- */
/* ---------------------------------------------------- */

/* General Layout & Backgrounds */
.ant-layout-sider {
  background: var(--color-sidebar) !important;
  color: var(--color-sidebar-foreground) !important;
}

.ant-layout-header {
  background: var(--color-header) !important;
  color: var(--color-header-foreground) !important;
  display: flex;
  align-items: center;
  padding: 0 16px;
}

.admin-layout-main {
  min-height: 100vh;
  margin:0;
  padding:0;
  /* background: var(--color-background); */
}

.admin-sider {
  overflow: auto;
  height: 100vh;
  background: var(--color-sidebar);
  box-shadow: 4px 0 32px rgba(210, 123, 73, 0.2);
}

.admin-logo-container {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 24px;
  border-bottom: 2px solid var(--color-sidebar-border);
  background: var(--color-header);
}

.admin-logo-container-collapsed {
  justify-content: center;
  padding: 0;
}

.admin-logo-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: var(--color-sidebar-accent);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-sidebar-accent-foreground);
  font-weight: bold;
  font-size: 18px;
}

.admin-text{
  /* background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-accent) 50%, #C56A3A 100%); */
  /* -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text; */
  color: var(--color-primary-foreground);
}

.admin-menu-container {
  /* padding: 16px 8px; */
  padding-right: 8px;
}

.admin-layout-content {
  transition: all 0.3s ease;
}

.admin-header {
  padding: 0 24px;
  background: var(--color-header);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 10;
  border-bottom: 2px solid var(--color-sidebar-border);
  box-shadow: 0 8px 32px rgba(210, 123, 73, 0.2);
  height: 64px;
}

.admin-button-header {
  font-size: 16px;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-foreground);
}

.admin-button-user {
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 16px;
  transition: all 0.2s ease;
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-foreground);
}

.admin-user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: var(--color-sidebar-accent);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-sidebar-accent-foreground);
  font-size: 14px;
  font-weight: bold;
}

/* Padded Content Area */
.admin-content-main {
  min-height: calc(100vh - 112px);
  padding: 24px;
  background: var(--color-background);
}

.admin-content-card {
  background: var(--color-card);
  border:none !important;
  /* color: var(--color-card-foreground); */
  /* border-radius: var(--radius); */
  /* padding: 24px; */
  /* box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1); */
}

/* Ant Menu Overrides */
.ant-menu {
  background: var(--color-sidebar) !important;
  color: var(--color-sidebar-foreground) !important;
  border-right: none !important;
}

.ant-menu-item,
.ant-menu-submenu-title {
  border-radius: var(--radius) !important;
  margin: 4px 8px !important;
  color: var(--color-sidebar-foreground) !important;
  transition: all 0.2s ease !important;
}

/* Updated Menu Item Selected State */
.ant-menu-item-selected {
  background: var(--color-sidebar-accent) !important;
  color: var(--color-sidebar-accent-foreground) !important;
  font-weight: 600 !important;
  box-shadow: 0 6px 20px rgba(163, 177, 138, 0.3) !important;
  border-left: none;
}

.ant-menu-item-selected .anticon {
  color: var(--color-sidebar-accent-foreground) !important;
}

/* Submenu parent active state */
.ant-menu-submenu-selected > .ant-menu-submenu-title {
  background: var(--color-sidebar-accent) !important;
  color: var(--color-sidebar-accent-foreground) !important;
  font-weight: 600 !important;
  box-shadow: 0 6px 20px rgba(163, 177, 138, 0.3) !important;
}

.ant-menu-submenu-selected > .ant-menu-submenu-title .anticon {
  color: var(--color-sidebar-accent-foreground) !important;
}

.ant-menu-item:hover,
.ant-menu-submenu-title:hover {
  background: var(--color-sidebar-accent) !important;
  color: var(--color-sidebar-accent-foreground) !important;
  transform: translateX(4px);
  box-shadow: 0 4px 16px rgba(163, 177, 138, 0.2) !important;
}

.ant-menu-item-selected:hover {
  background: var(--color-sidebar-accent) !important;
  color: var(--color-sidebar-accent-foreground) !important;
  transform: translateX(4px);
  box-shadow: 0 8px 24px rgba(163, 177, 138, 0.4) !important;
}

.ant-menu-sub.ant-menu-inline {
  background: transparent !important;
}

.ant-menu-sub .ant-menu-item {
  padding-left: 32px !important;
  margin-left: 8px !important;
  width: calc(100% - 16px) !important;
}

.header-button:hover {
  background: var(--color-accent) !important;
  color: var(--color-accent-foreground) !important;
  box-shadow: 0 6px 20px rgba(163, 177, 138, 0.2) !important;
}

.user-button:hover {
  background: var(--color-accent) !important;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(163, 177, 138, 0.25) !important;
  color: var(--color-accent-foreground) !important;
}

.ant-layout-content {
  background: var(--color-background);
}

/* Footer styles */
.admin-footer-main {
  text-align: center;
  padding: 16px;
  background: var(--color-primary);
  color: var(--color-background);
  /* border-top: 1px solid var(--color-border); */
}

/* Drawer styles */
.admin-drawer-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.admin-drawer-logo {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: var(--color-sidebar-accent);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-sidebar-accent-foreground);
  font-weight: bold;
  font-size: 18px;
}

/* Theme settings */
.admin-theme-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-radius: var(--radius);
  background: var(--color-card);
  border: 1px solid var(--color-border);
}

.admin-theme-text {
  display: flex;
  align-items: center;
  gap: 12px;
}

.admin-theme-description {
  font-size: 12px;
  color: var(--color-muted-foreground);
}

/* Mobile responsiveness */
@media (max-width: 991px) {
  .admin-layout-content {
    /* No margin needed as Ant Design handles mobile layout */
  }
}

@theme {
  --color-background: var(--color-background);
  --color-foreground: var(--color-foreground);
  --color-primary: var(--color-primary);
  --color-primary-foreground: var(--color-primary-foreground);
  --color-secondary: var(--color-secondary);
  --color-secondary-foreground: var(--color-secondary-foreground);
  --color-accent: var(--color-accent);
  --color-accent-foreground: var(--color-accent-foreground);
  --color-muted: var(--color-muted);
  --color-muted-foreground: var(--color-muted-foreground);
  --color-border: var(--color-border);
  --color-input: var(--color-input);
  --color-ring: var(--color-ring);
  

  --color-destructive: var(--color-destructive);
  --color-destructive-foreground: #fff; /* or your var if defined */
}

