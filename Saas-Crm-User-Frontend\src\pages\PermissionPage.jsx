import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
    Table,
    TableHeader,
    TableBody,
    TableRow,
    TableHead,
    TableCell
} from '@/components/ui/table';
import {
    <PERSON>alog,
    DialogTrigger,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';
import {
    AlertDialog,
    AlertDialogTrigger,
    AlertDialogContent,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogFooter,
    AlertDialogCancel,
    AlertDialogAction
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Pencil, Trash2 } from 'lucide-react';

export default function PermissionPage() {
    // Initial state for the list of permissions.
    const [permissions, setPermissions] = useState([
        { id: 1, name: 'create:users', description: 'Allows the creation of new user accounts.' },
        { id: 2, name: 'edit:posts', description: 'Grants the ability to edit any post.' },
        { id: 3, name: 'delete:files', description: 'Provides permission to delete files from the system.' },
    ]);

    // State to manage the visibility of the add and edit dialogs.
    const [showAdd, setShowAdd] = useState(false);
    const [showEdit, setShowEdit] = useState(false);

    // State to hold the permission being edited.
    const [currentPermission, setCurrentPermission] = useState(null);

    // State for the form inputs.
    const [form, setForm] = useState({ name: '', description: '' });

    // Handles adding a new permission to the state.
    const handleAddPermission = () => {
        const newPermission = {
            id: Date.now(), // Use a timestamp for a unique ID.
            name: form.name,
            description: form.description
        };
        setPermissions([...permissions, newPermission]);
        setForm({ name: '', description: '' }); // Clear the form.
        setShowAdd(false); // Close the dialog.
    };

    // Handles updating an existing permission in the state.
    const handleEditPermission = () => {
        setPermissions(
            // Map through the permissions and update the one that matches the current ID.
            permissions.map(permission => permission.id === currentPermission.id ? { ...permission, ...form } : permission)
        );
        setShowEdit(false); // Close the dialog.
        setForm({ name: '', description: '' }); // Clear the form.
    };

    // Handles deleting a permission from the state.
    const handleDeletePermission = id => {
        // Filter out the permission that matches the given ID.
        setPermissions(permissions.filter(permission => permission.id !== id));
    };

    // Opens the edit modal and pre-fills the form with the permission's data.
    const openEditModal = (permission) => {
        setCurrentPermission(permission);
        setForm({ name: permission.name, description: permission.description });
        setShowEdit(true);
    };

    return (
        <Card className="p-4 rounded-none">
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Permission Management</CardTitle>
                <Dialog open={showAdd} onOpenChange={setShowAdd}>
                    <DialogTrigger asChild>
                        <Button>Add Permission</Button>
                    </DialogTrigger>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Add Permission</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                            <div>
                                <Label htmlFor="name" className="pb-3">Name</Label>
                                <Input
                                    id="name"
                                    value={form.name}
                                    onChange={e => setForm({ ...form, name: e.target.value })}
                                />
                            </div>
                            <div>
                                <Label htmlFor="description" className="pb-3">Description</Label>
                                <Input
                                    id="description"
                                    value={form.description}
                                    onChange={e => setForm({ ...form, description: e.target.value })}
                                />
                            </div>
                        </div>
                        <DialogFooter>
                            <Button onClick={handleAddPermission}>Add</Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </CardHeader>

            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[50px]">#</TableHead>
                            <TableHead>Permission Name</TableHead>
                            <TableHead>Description</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {permissions.map((permission, index) => (
                            <TableRow key={permission.id}>
                                <TableCell>{index + 1}</TableCell>
                                <TableCell>{permission.name}</TableCell>
                                <TableCell>{permission.description}</TableCell>
                                <TableCell className="text-right space-x-2">
                                    {/* Edit Dialog */}
                                    <Dialog open={showEdit && currentPermission?.id === permission.id} onOpenChange={setShowEdit}>
                                        <DialogTrigger asChild>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => openEditModal(permission)}
                                            >
                                                <Pencil className="h-4 w-4" />
                                            </Button>
                                        </DialogTrigger>
                                        <DialogContent>
                                            <DialogHeader>
                                                <DialogTitle>Edit Permission</DialogTitle>
                                            </DialogHeader>
                                            <div className="space-y-4">
                                                <div>
                                                    <Label htmlFor="edit-name">Name</Label>
                                                    <Input
                                                        id="edit-name"
                                                        value={form.name}
                                                        onChange={e => setForm({ ...form, name: e.target.value })}
                                                    />
                                                </div>
                                                <div>
                                                    <Label htmlFor="edit-description">Description</Label>
                                                    <Input
                                                        id="edit-description"
                                                        value={form.description}
                                                        onChange={e => setForm({ ...form, description: e.target.value })}
                                                    />
                                                </div>
                                            </div>
                                            <DialogFooter>
                                                <Button onClick={handleEditPermission}>Update</Button>
                                            </DialogFooter>
                                        </DialogContent>
                                    </Dialog>

                                    {/* Delete Alert Dialog */}
                                    <AlertDialog>
                                        <AlertDialogTrigger asChild>
                                            <Button variant="destructive" size="sm">
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </AlertDialogTrigger>
                                        <AlertDialogContent>
                                            <AlertDialogHeader>
                                                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                            </AlertDialogHeader>
                                            <p>This action cannot be undone. This will permanently delete the permission.</p>
                                            <AlertDialogFooter>
                                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                <AlertDialogAction onClick={() => handleDeletePermission(permission.id)}>
                                                    Delete
                                                </AlertDialogAction>
                                            </AlertDialogFooter>
                                        </AlertDialogContent>
                                    </AlertDialog>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </CardContent>
        </Card>
    );
}
