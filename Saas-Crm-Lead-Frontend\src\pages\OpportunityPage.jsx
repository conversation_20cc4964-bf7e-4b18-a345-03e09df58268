import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
    Table,
    TableHeader,
    TableBody,
    TableRow,
    TableHead,
    TableCell
} from '@/components/ui/table';
import {
    <PERSON>alog,
    DialogTrigger,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';
import {
    AlertDialog,
    AlertDialogTrigger,
    AlertDialogContent,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogFooter,
    AlertDialogCancel,
    AlertDialogAction
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Pencil, Trash2, PlusCircle, MessageSquare, Paperclip, Mic, Calendar } from 'lucide-react';
import apiClient from '@/lib/apiClient';

export default function OpportunityPage() {
    const [leads, setLeads] = useState([
        {
            id: '1',
            name: '<PERSON>',
            email: '<EMAIL>',
            message: 'Interested in premium plan.',
            deadline: '2023-12-31',
            assignedTo: 'John Doe',
            leadType: 'New Lead',
            notes: [{ id: 'n1', text: 'Initial contact made.', date: new Date(2023, 0, 15, 10, 0) }],
            attachments: [{ id: 'a1', name: 'Proposal_v1.pdf', url: '#', uploadedAt: new Date(2023, 0, 16) }],
            recordings: [{ id: 'r1', fileName: 'call_recording_alice_1.wav', url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3', duration: '2:30', recordedAt: new Date(2023, 0, 17) }]
        },
        {
            id: '2',
            name: 'Bob Johnson',
            email: '<EMAIL>',
            message: 'Question about pricing.',
            deadline: '2024-01-15',
            assignedTo: 'Jane Smith',
            leadType: 'Existing Customer',
            notes: [{ id: 'n2', text: 'Sent pricing sheet.', date: new Date(2023, 1, 20, 14, 30) }, { id: 'n3', text: 'Followed up via email.', date: new Date(2023, 1, 22, 9, 0) }],
            attachments: [],
            recordings: [{ id: 'r2', fileName: 'call_recording_bob_1.wav', url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-2.mp3', duration: '1:45', recordedAt: new Date(2023, 1, 20) }]
        },
        {
            id: '3',
            name: 'Charlie Brown',
            email: '<EMAIL>',
            message: 'Looking for a demo.',
            deadline: '',
            assignedTo: '',
            leadType: 'Cold Lead',
            notes: [],
            attachments: [],
            recordings: []
        },
    ]);

    const availableUsers = ['Unassigned', 'John Doe', 'Jane Smith', 'Peter Jones', 'Sarah Lee'];
    const availableLeadTypes = ['New Lead', 'Cold Lead', 'Warm Lead', 'Hot Lead', 'Existing Customer', 'Partner Lead'];


    const [showAdd, setShowAdd] = useState(false);
    const [showEdit, setShowEdit] = useState(false);
    const [currentLead, setCurrentLead] = useState(null);
    const [form, setForm] = useState({ name: '', email: '', message: '', deadline: '', assignedTo: '', leadType: '' });
    const [newNoteText, setNewNoteText] = useState('');
    const [newAttachmentFile, setNewAttachmentFile] = useState(null);
    const [leadToDeleteId, setLeadToDeleteId] = useState(null);
    const [validationError, setValidationError] = useState('');
    const [activeTab, setActiveTab] = useState('general');

    useEffect(() => {

        apiClient.get('/leads').then((res) => {
            setLeads(res.data)
        })
    }, [])

    const handleInputChange = (e) => {
        const { id, value } = e.target;
        setForm(prev => ({ ...prev, [id]: value }));
        setValidationError('');
    };

    const handleFileChange = (e) => {
        setNewAttachmentFile(e.target.files[0]);
    };

    const handleAddLead = () => {
        if (!form.name || !form.email) {
            setValidationError("Name and Email are required.");
            return;
        }
        const newLead = {
            id: String(Date.now()),
            name: form.name,
            email: form.email,
            message: form.message,
            deadline: form.deadline,
            assignedTo: form.assignedTo,
            leadType: form.leadType,
            notes: [],
            attachments: [],
            recordings: [],
        };
        setLeads([newLead, ...leads]);
        setForm({ name: '', email: '', message: '', deadline: '', assignedTo: '', leadType: 'New Lead' });
        setValidationError('');
        setShowAdd(false);
    };

    const handleUpdateLeadGeneralInfo = () => {
        if (!form.name || !form.email) {
            setValidationError("Name and Email are required.");
            return;
        }
        setLeads(
            leads.map(lead =>
                lead.id === currentLead.id ? { ...lead, ...form } : lead
            )
        );
        setValidationError('');
        setShowEdit(false);
    };

    const handleAddNewNote = () => {
        if (newNoteText.trim() === '') {
            return;
        }
        if (currentLead) {
            const updatedNotes = [
                ...(currentLead.notes || []),
                {
                    id: String(Date.now()),
                    text: newNoteText,
                    date: new Date(),
                },
            ];
            setCurrentLead(prev => ({ ...prev, notes: updatedNotes }));
            setLeads(prevLeads =>
                prevLeads.map(lead =>
                    lead.id === currentLead.id ? { ...lead, notes: updatedNotes } : lead
                )
            );
            setNewNoteText('');
        }
    };

    const handleAddAttachment = () => {
        if (!newAttachmentFile) {
            return;
        }
        if (currentLead) {
            const updatedAttachments = [
                ...(currentLead.attachments || []),
                {
                    id: String(Date.now()),
                    name: newAttachmentFile.name,
                    url: URL.createObjectURL(newAttachmentFile),
                    uploadedAt: new Date(),
                },
            ];
            setCurrentLead(prev => ({ ...prev, attachments: updatedAttachments }));
            setLeads(prevLeads =>
                prevLeads.map(lead =>
                    lead.id === currentLead.id ? { ...lead, attachments: updatedAttachments } : lead
                )
            );
            setNewAttachmentFile(null);
            document.getElementById('attachment-input').value = '';
        }
    };

    const handleDeleteLeadConfirmed = () => {
        setLeads(leads.filter(lead => lead.id !== leadToDeleteId));
        setLeadToDeleteId(null);
    };

    const openAddModal = () => {
        setForm({ name: '', email: '', message: '', deadline: '', assignedTo: 'Unassigned', leadType: 'New Lead' });
        setValidationError('');
        setShowAdd(true);
    };

    const openEditModal = (lead) => {
        setCurrentLead(lead);
        setForm({
            name: lead.name,
            email: lead.email,
            message: lead.message,
            deadline: lead.deadline || '',
            assignedTo: lead.assignedTo || 'Unassigned',
            leadType: lead.leadType || 'New Lead'
        });
        setNewNoteText('');
        setNewAttachmentFile(null);
        setValidationError('');
        setActiveTab('general');
        setShowEdit(true);
    };

    const formatDate = (date) => {
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
        }).format(date);
    };

    return (
        <Card className="p-4 rounded-none">
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Lead Management</CardTitle>
                <Dialog open={showAdd} onOpenChange={setShowAdd}>
                    <DialogTrigger asChild>
                        <Button onClick={openAddModal}>
                            <PlusCircle className="mr-2 h-4 w-4" /> Add Lead
                        </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[800px] h-[90vh] flex flex-col">
                        <DialogHeader>
                            <DialogTitle>Add New Lead</DialogTitle>
                        </DialogHeader>
                        <div className="flex-grow overflow-y-auto pr-2">
                            <div className="space-y-4 py-4">
                                <div>
                                    <Label htmlFor="name" className="pb-3">Name</Label>
                                    <Input id="name" value={form.name} onChange={handleInputChange} />
                                </div>
                                <div>
                                    <Label htmlFor="email" className="pb-3">Email</Label>
                                    <Input id="email" type="email" value={form.email} onChange={handleInputChange} />
                                </div>
                                <div>
                                    <Label htmlFor="message" className="pb-3">Message</Label>
                                    <Textarea id="message" value={form.message} onChange={handleInputChange} />
                                </div>
                                <div>
                                    <Label htmlFor="deadline" className="pb-3">Deadline</Label>
                                    <Input id="deadline" type="date" value={form.deadline} onChange={handleInputChange} />
                                </div>
                                <div>
                                    <Label htmlFor="assignedTo" className="pb-3">Assigned To</Label>
                                    <select
                                        id="assignedTo"
                                        value={form.assignedTo}
                                        onChange={handleInputChange}
                                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    >
                                        {availableUsers.map(user => (
                                            <option key={user} value={user}>{user}</option>
                                        ))}
                                    </select>
                                </div>
                                <div>
                                    <Label htmlFor="leadType" className="pb-3">Lead Type</Label>
                                    <select
                                        id="leadType"
                                        value={form.leadType}
                                        onChange={handleInputChange}
                                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    >
                                        {availableLeadTypes.map(type => (
                                            <option key={type} value={type}>{type}</option>
                                        ))}
                                    </select>
                                </div>
                                {validationError && <p className="text-red-500 text-sm col-span-4">{validationError}</p>}
                            </div>
                        </div>
                        <DialogFooter className="mt-4 flex justify-end space-x-2">
                            <Button onClick={handleAddLead}>Add Lead</Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </CardHeader>

            <CardContent>
                {leads.length === 0 ? (
                    <div className="text-center text-gray-500 py-10">No leads found. Add a new one to get started!</div>
                ) : (
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead className="w-[50px]">#</TableHead>
                                <TableHead>Name</TableHead>
                                <TableHead>Email</TableHead>
                                <TableHead>Deadline</TableHead>
                                <TableHead>Assigned To</TableHead>
                                <TableHead>Lead Type</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {leads.map((lead, index) => (
                                <TableRow key={lead.id}>
                                    <TableCell>{index + 1}</TableCell>
                                    <TableCell>{lead.name}</TableCell>
                                    <TableCell>{lead.email}</TableCell>
                                    <TableCell>{lead.deadline}</TableCell>
                                    <TableCell>{lead.assignedTo}</TableCell>
                                    <TableCell>{lead.leadType}</TableCell>
                                    <TableCell className="text-right space-x-2">
                                        {/* Edit Dialog */}
                                        <Dialog open={showEdit && currentLead?.id === lead.id} onOpenChange={setShowEdit}>
                                            <DialogTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => openEditModal(lead)}
                                                >
                                                    <Pencil className="h-4 w-4" />
                                                </Button>
                                            </DialogTrigger>
                                            {currentLead && (
                                                <DialogContent className="sm:max-w-[800px] h-[90vh] flex flex-col">
                                                    <DialogHeader>
                                                        <DialogTitle className="text-2xl font-bold">{currentLead.name || 'Edit Lead'}</DialogTitle>
                                                    </DialogHeader>
                                                    <div className="flex-grow overflow-hidden flex flex-col">
                                                        {/* Tabs Navigation */}
                                                        <div className="border-b mb-4">
                                                            <nav className="-mb-px flex space-x-8" aria-label="Tabs">
                                                                <button
                                                                    onClick={() => setActiveTab('general')}
                                                                    className={`${activeTab === 'general' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                                                                >
                                                                    General
                                                                </button>
                                                                <button
                                                                    onClick={() => setActiveTab('notes')}
                                                                    className={`${activeTab === 'notes' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                                                                >
                                                                    Notes
                                                                </button>
                                                                <button
                                                                    onClick={() => setActiveTab('attachments')}
                                                                    className={`${activeTab === 'attachments' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                                                                >
                                                                    Attachments
                                                                </button>
                                                                <button
                                                                    onClick={() => setActiveTab('recordings')}
                                                                    className={`${activeTab === 'recordings' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                                                                >
                                                                    Recordings
                                                                </button>
                                                            </nav>
                                                        </div>

                                                        {/* Tab Content */}
                                                        <div className="flex-grow overflow-y-auto pr-2">
                                                            {activeTab === 'general' && (
                                                                <div className="space-y-6 py-4">
                                                                    {/* Basic Information Block */}
                                                                    <div className="border p-4 rounded-md bg-gray-50 space-y-4">
                                                                        <h4 className="text-lg font-semibold mb-2">Basic Information</h4>
                                                                        <div className="grid grid-cols-4 items-center gap-4">
                                                                            <Label htmlFor="name" className="text-right">Name</Label>
                                                                            <Input id="name" value={form.name} onChange={handleInputChange} className="col-span-3" />
                                                                        </div>
                                                                        <div className="grid grid-cols-4 items-center gap-4">
                                                                            <Label htmlFor="email" className="text-right">Email</Label>
                                                                            <Input id="email" type="email" value={form.email} onChange={handleInputChange} className="col-span-3" />
                                                                        </div>
                                                                        <div className="grid grid-cols-4 items-center gap-4">
                                                                            <Label htmlFor="message" className="text-right">Message</Label>
                                                                            <Textarea id="message" value={form.message} onChange={handleInputChange} className="col-span-3 h-24 resize-y" />
                                                                        </div>
                                                                    </div>

                                                                    {/* Assignment & Deadline Block */}
                                                                    <div className="border p-4 rounded-md bg-gray-50 space-y-4">
                                                                        <h4 className="text-lg font-semibold mb-2">Assignment & Deadline</h4>
                                                                        <div className="grid grid-cols-4 items-center gap-4">
                                                                            <Label htmlFor="deadline" className="text-right">Deadline</Label>
                                                                            <Input id="deadline" type="date" value={form.deadline} onChange={handleInputChange} className="col-span-3" />
                                                                        </div>
                                                                        <div className="grid grid-cols-4 items-center gap-4">
                                                                            <Label htmlFor="assignedTo" className="text-right">Assigned To</Label>
                                                                            <select
                                                                                id="assignedTo"
                                                                                value={form.assignedTo}
                                                                                onChange={handleInputChange}
                                                                                className="col-span-3 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                                                            >
                                                                                {availableUsers.map(user => (
                                                                                    <option key={user} value={user}>{user}</option>
                                                                                ))}
                                                                            </select>
                                                                        </div>
                                                                        <div className="grid grid-cols-4 items-center gap-4">
                                                                            <Label htmlFor="leadType" className="text-right">Lead Type</Label>
                                                                            <select
                                                                                id="leadType"
                                                                                value={form.leadType}
                                                                                onChange={handleInputChange}
                                                                                className="col-span-3 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                                                            >
                                                                                {availableLeadTypes.map(type => (
                                                                                    <option key={type} value={type}>{type}</option>
                                                                                ))}
                                                                            </select>
                                                                        </div>
                                                                    </div>

                                                                    {validationError && <p className="text-red-500 text-sm col-span-4">{validationError}</p>}
                                                                </div>
                                                            )}

                                                            {activeTab === 'notes' && (
                                                                <div className="space-y-3 pt-4">
                                                                    <h3 className="text-lg font-semibold flex items-center">
                                                                        <MessageSquare className="mr-2 h-5 w-5 text-gray-600" /> Notes
                                                                    </h3>
                                                                    <div className="max-h-96 overflow-y-auto pr-2 space-y-2">
                                                                        {currentLead?.notes && currentLead.notes.length > 0 ? (
                                                                            currentLead.notes.map(note => (
                                                                                <div key={note.id} className="p-2 bg-gray-50 rounded-md text-sm border border-gray-200">
                                                                                    <p className="text-gray-800">{note.text}</p>
                                                                                    <p className="text-xs text-gray-500 mt-1">{formatDate(note.date)}</p>
                                                                                </div>
                                                                            ))
                                                                        ) : (
                                                                            <p className="text-sm text-gray-500">No notes yet.</p>
                                                                        )}
                                                                    </div>
                                                                    <div className="flex space-x-2 mt-3">
                                                                        <Textarea
                                                                            placeholder="Add a new note..."
                                                                            value={newNoteText}
                                                                            onChange={(e) => setNewNoteText(e.target.value)}
                                                                            className="flex-grow resize-y"
                                                                            rows={2}
                                                                        />
                                                                        <Button onClick={handleAddNewNote} className="shrink-0 self-end">Add Note</Button>
                                                                    </div>
                                                                </div>
                                                            )}

                                                            {activeTab === 'attachments' && (
                                                                <div className="space-y-3 pt-4">
                                                                    <h3 className="text-lg font-semibold flex items-center">
                                                                        <Paperclip className="mr-2 h-5 w-5 text-gray-600" /> Attachments
                                                                    </h3>
                                                                    <div className="max-h-96 overflow-y-auto pr-2 space-y-2">
                                                                        {currentLead?.attachments && currentLead.attachments.length > 0 ? (
                                                                            currentLead.attachments.map(attachment => (
                                                                                <div key={attachment.id} className="p-2 bg-gray-50 rounded-md text-sm border border-gray-200 flex items-center justify-between">
                                                                                    <a href={attachment.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline flex items-center">
                                                                                        <Paperclip className="h-4 w-4 mr-2" /> {attachment.name}
                                                                                    </a>
                                                                                    <span className="text-xs text-gray-500">{formatDate(attachment.uploadedAt)}</span>
                                                                                </div>
                                                                            ))
                                                                        ) : (
                                                                            <p className="text-sm text-gray-500">No attachments yet.</p>
                                                                        )}
                                                                    </div>
                                                                    <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 mt-3 items-end">
                                                                        <Input
                                                                            id="attachment-input"
                                                                            type="file"
                                                                            onChange={handleFileChange}
                                                                            className="flex-grow"
                                                                        />
                                                                        <Button onClick={handleAddAttachment} disabled={!newAttachmentFile} className="shrink-0">Attach File</Button>
                                                                    </div>
                                                                    <p className="text-xs text-gray-500 mt-1">Note: In a real app, files would be uploaded to a server.</p>
                                                                </div>
                                                            )}

                                                            {activeTab === 'recordings' && (
                                                                <div className="space-y-3 pt-4">
                                                                    <h3 className="text-lg font-semibold flex items-center">
                                                                        <Mic className="mr-2 h-5 w-5 text-gray-600" /> Recordings
                                                                    </h3>
                                                                    <div className="max-h-96 overflow-y-auto pr-2 space-y-2">
                                                                        {currentLead?.recordings && currentLead.recordings.length > 0 ? (
                                                                            currentLead.recordings.map(recording => (
                                                                                <div key={recording.id} className="p-2 bg-gray-50 rounded-md text-sm border border-gray-200">
                                                                                    <div className="flex items-center space-x-2 mb-2">
                                                                                        <Mic className="h-4 w-4" />
                                                                                        <span className="font-medium text-gray-800">{recording.fileName}</span>
                                                                                    </div>
                                                                                    <audio controls src={recording.url} className="w-full"></audio>
                                                                                    <p className="text-xs text-gray-500 mt-1">Duration: {recording.duration} | Recorded: {formatDate(recording.recordedAt)}</p>
                                                                                </div>
                                                                            ))
                                                                        ) : (
                                                                            <p className="text-sm text-gray-500">No recordings yet.</p>
                                                                        )}
                                                                    </div>
                                                                    <p className="text-xs text-gray-500 mt-1">Note: Actual audio files require server-side storage.</p>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                    <DialogFooter className="mt-4 flex justify-end space-x-2">
                                                        <Button variant="outline" onClick={() => setShowEdit(false)}>Close</Button>
                                                        <Button onClick={handleUpdateLeadGeneralInfo}>Save Changes</Button>
                                                    </DialogFooter>
                                                </DialogContent>
                                            )}
                                        </Dialog>

                                        {/* Delete Alert Dialog */}
                                        <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button
                                                    variant="destructive"
                                                    size="sm"
                                                    onClick={() => setLeadToDeleteId(lead.id)}
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                                </AlertDialogHeader>
                                                <p>This action cannot be undone. This will permanently delete the lead.</p>
                                                <AlertDialogFooter>
                                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                    <AlertDialogAction onClick={handleDeleteLeadConfirmed}>
                                                        Delete
                                                    </AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                )}
            </CardContent>
        </Card>
    );
}
