import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
    Table,
    TableHeader,
    TableBody,
    TableRow,
    TableHead,
    TableCell
} from '@/components/ui/table';
import {
    <PERSON>alog,
    DialogTrigger,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';
import {
    AlertDialog,
    AlertDialogTrigger,
    AlertDialogContent,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogFooter,
    AlertDialogCancel,
    AlertDialogAction
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Pencil, Trash2 } from 'lucide-react';

export default function RolePage() {
    // Initial state for the list of roles.
    const [roles, setRoles] = useState([
        { id: 1, name: 'Admin', description: 'Can manage all system settings.' },
        { id: 2, name: 'Editor', description: 'Can create and edit content.' },
        { id: 3, name: 'Viewer', description: 'Can only view public content.' },
    ]);

    // State to manage the visibility of the add and edit dialogs.
    const [showAdd, setShowAdd] = useState(false);
    const [showEdit, setShowEdit] = useState(false);

    // State to hold the role being edited.
    const [currentRole, setCurrentRole] = useState(null);

    // State for the form inputs.
    const [form, setForm] = useState({ name: '', description: '' });

    // Handles adding a new role to the state.
    const handleAddRole = () => {
        const newRole = {
            id: Date.now(), // Use a timestamp for a unique ID.
            name: form.name,
            description: form.description
        };
        setRoles([...roles, newRole]);
        setForm({ name: '', description: '' }); // Clear the form.
        setShowAdd(false); // Close the dialog.
    };

    // Handles updating an existing role in the state.
    const handleEditRole = () => {
        setRoles(
            // Map through the roles and update the one that matches the current ID.
            roles.map(role => role.id === currentRole.id ? { ...role, ...form } : role)
        );
        setShowEdit(false); // Close the dialog.
        setForm({ name: '', description: '' }); // Clear the form.
    };

    // Handles deleting a role from the state.
    const handleDeleteRole = id => {
        // Filter out the role that matches the given ID.
        setRoles(roles.filter(role => role.id !== id));
    };

    // Opens the edit modal and pre-fills the form with the role's data.
    const openEditModal = (role) => {
        setCurrentRole(role);
        setForm({ name: role.name, description: role.description });
        setShowEdit(true);
    };

    return (
        <Card className="p-4 rounded-none">
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Role Management</CardTitle>
                <Dialog open={showAdd} onOpenChange={setShowAdd}>
                    <DialogTrigger asChild>
                        <Button>Add Role</Button>
                    </DialogTrigger>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Add Role</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                            <div>
                                <Label htmlFor="name" className="pb-3">Name</Label>
                                <Input
                                    id="name"
                                    value={form.name}
                                    onChange={e => setForm({ ...form, name: e.target.value })}
                                />
                            </div>
                            <div>
                                <Label htmlFor="description" className="pb-3">Description</Label>
                                <Input
                                    id="description"
                                    value={form.description}
                                    onChange={e => setForm({ ...form, description: e.target.value })}
                                />
                            </div>
                        </div>
                        <DialogFooter>
                            <Button onClick={handleAddRole}>Add</Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </CardHeader>

            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[50px]">#</TableHead>
                            <TableHead>Role Name</TableHead>
                            <TableHead>Description</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {roles.map((role, index) => (
                            <TableRow key={role.id}>
                                <TableCell>{index + 1}</TableCell>
                                <TableCell>{role.name}</TableCell>
                                <TableCell>{role.description}</TableCell>
                                <TableCell className="text-right space-x-2">
                                    {/* Edit Dialog */}
                                    <Dialog open={showEdit && currentRole?.id === role.id} onOpenChange={setShowEdit}>
                                        <DialogTrigger asChild>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => openEditModal(role)}
                                            >
                                                <Pencil className="h-4 w-4" />
                                            </Button>
                                        </DialogTrigger>
                                        <DialogContent>
                                            <DialogHeader>
                                                <DialogTitle>Edit Role</DialogTitle>
                                            </DialogHeader>
                                            <div className="space-y-4">
                                                <div>
                                                    <Label htmlFor="edit-name">Name</Label>
                                                    <Input
                                                        id="edit-name"
                                                        value={form.name}
                                                        onChange={e => setForm({ ...form, name: e.target.value })}
                                                    />
                                                </div>
                                                <div>
                                                    <Label htmlFor="edit-description">Description</Label>
                                                    <Input
                                                        id="edit-description"
                                                        value={form.description}
                                                        onChange={e => setForm({ ...form, description: e.target.value })}
                                                    />
                                                </div>
                                            </div>
                                            <DialogFooter>
                                                <Button onClick={handleEditRole}>Update</Button>
                                            </DialogFooter>
                                        </DialogContent>
                                    </Dialog>

                                    {/* Delete Alert Dialog */}
                                    <AlertDialog>
                                        <AlertDialogTrigger asChild>
                                            <Button variant="destructive" size="sm">
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </AlertDialogTrigger>
                                        <AlertDialogContent>
                                            <AlertDialogHeader>
                                                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                            </AlertDialogHeader>
                                            <p>This action cannot be undone. This will permanently delete the role.</p>
                                            <AlertDialogFooter>
                                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                <AlertDialogAction onClick={() => handleDeleteRole(role.id)}>
                                                    Delete
                                                </AlertDialogAction>
                                            </AlertDialogFooter>
                                        </AlertDialogContent>
                                    </AlertDialog>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </CardContent>
        </Card>
    );
}
